"""
Classification Evaluation Script

This script evaluates classification results against true data, similar to the extraction evaluation.
It compares document type classifications for each page and generates detailed reports with the same
structure and metadata as the extraction evaluation.

Key Features:
- Compares page-by-page document type classifications
- Generates Excel reports with formatting and statistics
- Includes PDF hyperlinks (when PDFs are available)
- Provides comprehensive logging and error handling
- Supports evaluation of subdirectories
- Creates summary reports comparing multiple runs

Data Structure:
- Output data: data/output_data/classification/*_bedrock_response.json
- True data: test_json/*.json
- Reports: data/evaluation/classification_*.xlsx

Usage Examples:

1. Command Line:
    python app/evaluation_classification.py --output-dir data/output_data/classification --true-dir test_json

2. Python Functions:
    import app.evaluation_classification as eval_cls

    # Evaluate main classification directory
    result = eval_cls.run_classification_evaluation()

    # Evaluate all subdirectories
    all_results = eval_cls.evaluate_all_subdirectories()

    # Create summary report
    summary_file = eval_cls.create_summary_report(all_results)

    # Run verification/dry run
    eval_cls.dry_run_evaluation()
    eval_cls.verify_evaluation_structure()

3. Evaluate specific subdirectory:
    result = eval_cls.run_evaluation_for_subdirectory("run1")

Report Structure (matches extraction evaluation):
- File Name: Base filename
- Field Name: Page_X_doc_type (e.g., Page_1_doc_type)
- True Data Value: Expected document type
- Extracted Value: Predicted document type
- Confidence: (placeholder for future confidence data)
- Correct: TRUE/FALSE/NOT_CLASSIFIED/TRUE_DATA_NOT_FOUND
- PDF Link: Hyperlink to original PDF (when available)
- Additional metadata columns
"""

import json
import os
import glob
import argparse
import pandas as pd
import csv
from datetime import datetime
from pathlib import Path

try:
    from openpyxl import Workbook
    from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("Warning: openpyxl not available. Will create CSV report instead.")

def setup_logging():
    """Setup basic logging for evaluation."""
    import logging
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"classification_evaluation_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )
    
    return logging.getLogger(__name__)

def load_json_file(file_path):
    """Load and parse JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_classification_results(data):
    """Extract classification results from the JSON data."""
    if not data:
        return []

    # Extract from analysis_result.documents (for output files)
    if 'analysis_result' in data and 'documents' in data['analysis_result']:
        return data['analysis_result']['documents']

    # Extract from documents (for true data files)
    if 'documents' in data:
        return data['documents']

    return []

def extract_confidence_values(data):
    """Extract confidence values from bedrock response if available."""
    confidence_values = {}
    
    # For classification, confidence might be in the bedrock response
    # This is a placeholder - adjust based on actual confidence data structure
    if data and 'bedrock_response' in data:
        # Add confidence extraction logic here if available in the response
        pass
    
    return confidence_values

def find_pdf_file(base_filename, logger):
    """
    Find PDF file in classification folder or processed subfolder.

    Args:
        base_filename (str): Base filename without extension
        logger: Logger instance

    Returns:
        str: Path to PDF file if found, empty string otherwise
    """
    # Possible PDF locations to check - prioritize test_output/processed
    pdf_locations = [
        f"test_output/processed/{base_filename}.pdf",
        f"test_output/{base_filename}.pdf",
        f"data/input_data/classification/{base_filename}.pdf",
        f"data/input_data/classification/processed/{base_filename}.pdf",
        f"data/output_data/classification/{base_filename}.pdf",
        f"data/output_data/classification/processed/{base_filename}.pdf"
    ]

    for pdf_path in pdf_locations:
        if os.path.exists(pdf_path):
            logger.debug(f"Found PDF at: {pdf_path}")
            return pdf_path

    logger.warning(f"PDF not found for {base_filename} in any of the expected locations")
    return ""

def find_page_pdf_file(base_filename, page_no, doc_type, logger):
    """
    Find page-specific PDF file in test_output/{order_id}/ folder.

    Args:
        base_filename (str): Base filename (order_id)
        page_no (int): Page number
        doc_type (str): Document type
        logger: Logger instance

    Returns:
        str: Path to page PDF file if found, empty string otherwise
    """
    # Look for page-specific PDF in order folder
    order_folder = f"test_output/{base_filename}"

    if not os.path.exists(order_folder):
        logger.debug(f"Order folder not found: {order_folder}")
        return ""

    # List all files in the order folder
    try:
        files = os.listdir(order_folder)

        # Look for files that start with the page number and contain the doc_type
        for file in files:
            if file.endswith('.pdf'):
                # Check if file starts with page number and contains doc_type
                # Format: {page_number}_{doc_type}_{filename}.pdf or {page_range}_{doc_type}_{filename}.pdf
                if file.startswith(f"{page_no}_") and f"_{doc_type}_" in file:
                    page_pdf_path = os.path.join(order_folder, file)
                    logger.debug(f"Found page PDF at: {page_pdf_path}")
                    return page_pdf_path

                # Also check for page ranges like "2-3_pod_filename.pdf" where page_no falls in range
                if "-" in file.split("_")[0]:
                    try:
                        page_range = file.split("_")[0]
                        if "-" in page_range:
                            start_page, end_page = map(int, page_range.split("-"))
                            if start_page <= page_no <= end_page and f"_{doc_type}_" in file:
                                page_pdf_path = os.path.join(order_folder, file)
                                logger.debug(f"Found page PDF in range at: {page_pdf_path}")
                                return page_pdf_path
                    except (ValueError, IndexError):
                        continue

        logger.debug(f"Page PDF not found for {base_filename}, page {page_no}, doc_type {doc_type}")
        return ""

    except Exception as e:
        logger.warning(f"Error listing files in {order_folder}: {e}")
        return ""

def get_pdf_path_from_source_file(source_file, base_filename=None, logger=None):
    """Extract PDF path from source_file field and verify it exists."""
    if source_file:
        # Convert to relative path from the workspace root
        if source_file.startswith('/home/<USER>/Documents/repositories/logistically/'):
            relative_path = source_file.replace('/home/<USER>/Documents/repositories/logistically/', '')
        else:
            relative_path = source_file

        # Check if the source file path exists
        if os.path.exists(relative_path):
            return relative_path

        # If source file doesn't exist, try to find PDF using base filename
        if base_filename and logger:
            return find_pdf_file(base_filename, logger)

    # If no source file provided, try to find PDF using base filename
    if base_filename and logger:
        return find_pdf_file(base_filename, logger)

    return ""

def evaluate_single_file(output_file, true_file, file_name, logger):
    """Evaluate a single file pair and return comparison results."""
    logger.info(f"Evaluating: {output_file} vs {true_file}")

    # Load both files
    output_data = load_json_file(output_file)
    true_data = load_json_file(true_file)

    if not output_data or not true_data:
        logger.error(f"Failed to load data files")
        return None

    # Extract classification results
    output_docs = extract_classification_results(output_data)
    true_docs = extract_classification_results(true_data)

    # Get PDF path - try source file first, then search for PDF
    pdf_path = get_pdf_path_from_source_file(
        output_data.get('source_file', ''),
        base_filename=file_name,
        logger=logger
    )

    # Create a mapping of page_no to doc_type for easier comparison
    output_mapping = {doc['page_no']: doc['doc_type'] for doc in output_docs}
    true_mapping = {doc['page_no']: doc['doc_type'] for doc in true_docs}

    # Get all unique page numbers from both datasets
    all_pages = set(output_mapping.keys()) | set(true_mapping.keys())

    results = []
    for page_no in sorted(all_pages):
        output_doc_type = output_mapping.get(page_no, "NOT_CLASSIFIED")
        true_doc_type = true_mapping.get(page_no, "NOT_FOUND")

        is_correct = output_doc_type == true_doc_type

        # Determine correctness status
        if true_doc_type == "NOT_FOUND":
            correct_status = "TRUE_DATA_NOT_FOUND"
        elif output_doc_type == "NOT_CLASSIFIED":
            correct_status = "NOT_CLASSIFIED"
        else:
            correct_status = "TRUE" if is_correct else "FALSE"

        # Find page-specific PDF file
        # Use the true doc_type if available, otherwise use output doc_type
        doc_type_for_search = true_doc_type if true_doc_type != "NOT_FOUND" else output_doc_type
        page_pdf_path = ""
        if doc_type_for_search != "NOT_CLASSIFIED":
            page_pdf_path = find_page_pdf_file(file_name, page_no, doc_type_for_search, logger)

        results.append({
            'File Name': file_name,
            'Field Name': f"Page_{page_no}_doc_type",
            'True Data Value': true_doc_type,
            'Extracted Value': output_doc_type,
            'Confidence': "",  # Placeholder for confidence if available
            'Correct': correct_status,
            'PDF Link': pdf_path,
            'Page Link': page_pdf_path,
            'Moved PDF Path': pdf_path,  # Same as PDF Link for classification
            'Extracted JSON Path': output_file
        })

    return results

def create_csv_report(all_results, output_file, logger):
    """Create CSV report for classification evaluation."""
    logger.info(f"Creating CSV report: {output_file}")
    
    # Change extension to .csv
    csv_file = output_file.replace('.xlsx', '.csv')
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Write header
        writer.writerow(['File Name', 'Field Name', 'True Data Value', 'Extracted Value',
                        'Confidence', 'Correct', 'PDF Link', 'Page Link', 'Moved PDF Path', 'Extracted JSON Path'])

        # Write all results
        for file_name, results in all_results.items():
            for result in results:
                writer.writerow([
                    result['File Name'],
                    result['Field Name'],
                    result['True Data Value'],
                    result['Extracted Value'],
                    result['Confidence'],
                    result['Correct'],
                    result['PDF Link'],
                    result['Page Link'],
                    result['Moved PDF Path'],
                    result['Extracted JSON Path']
                ])
        
        # Write statistics summary
        writer.writerow([])
        writer.writerow(['=== STATISTICS SUMMARY ==='])
        
        # Calculate overall statistics
        all_field_results = []
        for results in all_results.values():
            all_field_results.extend(results)
        
        if all_field_results:
            total_count = len(all_field_results)
            correct_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
            false_count = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
            not_classified_count = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
            true_data_not_found_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")
            
            comparable_count = total_count - not_classified_count - true_data_not_found_count
            accuracy = round((correct_count / comparable_count * 100) if comparable_count > 0 else 0, 2)
            
            writer.writerow(['Total Classifications', total_count])
            writer.writerow(['Correct', correct_count])
            writer.writerow(['Incorrect', false_count])
            writer.writerow(['Not Classified', not_classified_count])
            writer.writerow(['True Data Not Found', true_data_not_found_count])
            writer.writerow(['Accuracy (%)', accuracy])
    
    logger.info(f"CSV report saved to: {csv_file}")

def create_excel_report(all_results, output_file, logger):
    """Create Excel report with formatting."""
    logger.info(f"Creating Excel report: {output_file}")
    
    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Classification Evaluation"
    
    # Add headers
    headers = ['File Name', 'Field Name', 'True Data Value', 'Extracted Value',
               'Confidence', 'Correct', 'PDF Link', 'Page Link', 'Moved PDF Path', 'Extracted JSON Path']
    ws.append(headers)
    
    # Style headers
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True)
    
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=1, column=col)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # Add data rows
    row_idx = 2
    for file_name, results in all_results.items():
        for result in results:
            ws.cell(row=row_idx, column=1, value=result['File Name'])
            ws.cell(row=row_idx, column=2, value=result['Field Name'])
            ws.cell(row=row_idx, column=3, value=result['True Data Value'])
            ws.cell(row=row_idx, column=4, value=result['Extracted Value'])
            ws.cell(row=row_idx, column=5, value=result['Confidence'])
            ws.cell(row=row_idx, column=6, value=result['Correct'])
            
            # Add PDF hyperlink if path exists
            pdf_path = result['PDF Link']
            pdf_cell = ws.cell(row=row_idx, column=7)
            if pdf_path and os.path.exists(pdf_path):
                # Create hyperlink to PDF file
                pdf_cell.hyperlink = f"file:///{os.path.abspath(pdf_path)}"
                pdf_cell.value = "Open PDF"
                pdf_cell.font = Font(color="0000FF", underline="single")
                logger.debug(f"Added PDF hyperlink: {pdf_path}")
            else:
                pdf_cell.value = "PDF not found"
                pdf_cell.font = Font(color="FF0000")  # Red text for missing PDFs
                logger.warning(f"PDF not found for {result['File Name']}: {pdf_path}")

            # Add Page PDF hyperlink if path exists
            page_pdf_path = result['Page Link']
            page_pdf_cell = ws.cell(row=row_idx, column=8)
            if page_pdf_path and os.path.exists(page_pdf_path):
                # Create hyperlink to page PDF file
                page_pdf_cell.hyperlink = f"file:///{os.path.abspath(page_pdf_path)}"
                page_pdf_cell.value = "Open Page"
                page_pdf_cell.font = Font(color="0000FF", underline="single")
                logger.debug(f"Added Page PDF hyperlink: {page_pdf_path}")
            else:
                page_pdf_cell.value = "Page not found"
                page_pdf_cell.font = Font(color="FF0000")  # Red text for missing page PDFs
                if page_pdf_path:  # Only log warning if we tried to find a page PDF
                    logger.debug(f"Page PDF not found for {result['File Name']}: {page_pdf_path}")

            ws.cell(row=row_idx, column=9, value=result['Moved PDF Path'])
            ws.cell(row=row_idx, column=10, value=result['Extracted JSON Path'])
            
            # Apply formatting based on correctness
            if result['Correct'] == "TRUE":
                fill_color = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")  # Light green
            elif result['Correct'] == "FALSE":
                fill_color = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")  # Light red
            elif result['Correct'] == "NOT_CLASSIFIED":
                fill_color = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")  # Light yellow
            else:  # TRUE_DATA_NOT_FOUND
                fill_color = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")  # Light blue
            
            # Apply fill to the entire row
            for col in range(1, len(headers) + 1):
                cell = ws.cell(row=row_idx, column=col)
                cell.fill = fill_color
            
            row_idx += 1
    
    # Add statistics at the end
    stats_start_row = row_idx + 2
    ws.cell(row=stats_start_row, column=1, value="CLASSIFICATION STATISTICS")
    ws.cell(row=stats_start_row, column=1).font = Font(bold=True, size=14)
    stats_start_row += 1
    
    # Calculate overall statistics
    all_field_results = []
    for results in all_results.values():
        all_field_results.extend(results)
    
    if all_field_results:
        total_count = len(all_field_results)
        correct_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
        false_count = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
        not_classified_count = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
        true_data_not_found_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")
        
        comparable_count = total_count - not_classified_count - true_data_not_found_count
        accuracy = round((correct_count / comparable_count * 100) if comparable_count > 0 else 0, 2)
        
        stats = [
            ("Total Classifications", total_count),
            ("Correct", correct_count),
            ("Incorrect", false_count),
            ("Not Classified", not_classified_count),
            ("True Data Not Found", true_data_not_found_count),
            ("Accuracy (%)", accuracy)
        ]
        
        for stat_name, stat_value in stats:
            ws.cell(row=stats_start_row, column=1, value=stat_name)
            ws.cell(row=stats_start_row, column=2, value=stat_value)
            stats_start_row += 1
    
    # Set optimal column widths
    column_widths = {
        'A': 15,  # File Name
        'B': 20,  # Field Name
        'C': 18,  # True Data Value
        'D': 18,  # Extracted Value
        'E': 12,  # Confidence
        'F': 10,  # Correct
        'G': 12,  # PDF Link
        'H': 12,  # Page Link
        'I': 35,  # Moved PDF Path
        'J': 40   # Extracted JSON Path
    }

    for col_letter, width in column_widths.items():
        ws.column_dimensions[col_letter].width = width

    # Auto-adjust based on content if needed
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if cell.value and len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass

        # Use predefined width or calculated width, whichever is larger
        predefined_width = column_widths.get(column_letter, 15)
        calculated_width = min(max_length + 2, 60)  # Cap at 60 characters
        final_width = max(predefined_width, calculated_width)
        ws.column_dimensions[column_letter].width = final_width
    
    # Save workbook
    wb.save(output_file)
    logger.info(f"Excel report saved to: {output_file}")

def find_file_pairs(output_dir, true_dir, logger):
    """Find matching pairs of output and true data files."""
    output_files = glob.glob(os.path.join(output_dir, "*_bedrock_response.json"))
    file_pairs = []

    for output_file in output_files:
        # Extract base filename
        output_filename = os.path.basename(output_file)
        base_name = output_filename.replace("_bedrock_response.json", "")

        # Look for corresponding true data file (without _bedrock_response suffix)
        true_file = os.path.join(true_dir, f"{base_name}.json")

        if os.path.exists(true_file):
            file_pairs.append((output_file, true_file, base_name))
            logger.info(f"Found pair: {base_name}")
        else:
            logger.warning(f"No true data file found for: {base_name} (looking for {true_file})")

    return file_pairs

def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate classification results against true data')
    parser.add_argument('--output-dir', '-o', default='data/output_data/classification',
                       help='Directory containing output classification files')
    parser.add_argument('--true-dir', '-t', default='test_json',
                       help='Directory containing true data classification files')
    parser.add_argument('--output-file', '-f', default='data/evaluation/classification_evaluation_report.xlsx',
                       help='Output Excel file path')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting classification evaluation process")
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Find file pairs
    file_pairs = find_file_pairs(args.output_dir, args.true_dir, logger)
    
    if not file_pairs:
        logger.error("No matching file pairs found!")
        return
    
    # Evaluate all files
    all_results = {}
    for output_file, true_file, base_name in file_pairs:
        results = evaluate_single_file(output_file, true_file, base_name, logger)
        if results:
            all_results[base_name] = results
    
    if not all_results:
        logger.error("No successful evaluations!")
        return
    
    # Create report
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, args.output_file, logger)
    else:
        create_csv_report(all_results, args.output_file, logger)
    
    # Print summary
    logger.info("\n=== CLASSIFICATION EVALUATION SUMMARY ===")
    
    all_field_results = []
    for results in all_results.values():
        all_field_results.extend(results)
    
    if all_field_results:
        total = len(all_field_results)
        correct = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
        incorrect = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
        not_classified = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
        true_data_not_found = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")
        
        comparable = total - not_classified - true_data_not_found
        accuracy = (correct / comparable * 100) if comparable > 0 else 0
        
        logger.info(f"Total Classifications: {total}")
        logger.info(f"Correct: {correct}")
        logger.info(f"Incorrect: {incorrect}")
        logger.info(f"Not Classified: {not_classified}")
        logger.info(f"True Data Not Found: {true_data_not_found}")
        logger.info(f"Accuracy: {accuracy:.1f}%")

def run_classification_evaluation(output_dir="data/output_data/classification",
                                 true_dir="test_json",
                                 output_file=None):
    """
    Run classification evaluation with direct function call (for VS Code terminal usage).

    Args:
        output_dir (str): Directory containing output classification files
        true_dir (str): Directory containing true data classification files
        output_file (str): Output Excel file path (optional, auto-generated if None)

    Returns:
        dict: Evaluation results summary
    """
    from datetime import datetime

    # Setup logging
    logger = setup_logging()
    logger.info("Starting classification evaluation process")

    # Auto-generate output file if not provided
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"data/evaluation/classification_evaluation_{timestamp}.xlsx"

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    logger.info(f"Output directory: {output_dir}")
    logger.info(f"True data directory: {true_dir}")

    # Find file pairs
    file_pairs = find_file_pairs(output_dir, true_dir, logger)

    if not file_pairs:
        logger.error("No matching file pairs found!")
        return None

    # Evaluate all files
    all_results = {}
    logger.info(f"Found {len(file_pairs)} file pairs to evaluate")

    for i, (output_file_path, true_file_path, base_name) in enumerate(file_pairs, 1):
        logger.info(f"Processing file {i}/{len(file_pairs)}: {base_name}")
        results = evaluate_single_file(output_file_path, true_file_path, base_name, logger)
        if results:
            all_results[base_name] = results
            logger.info(f"  ✅ Evaluation successful for {base_name}")
        else:
            logger.warning(f"  ❌ Evaluation failed for {base_name}")

    logger.info(f"Successfully evaluated {len(all_results)} out of {len(file_pairs)} files")

    if not all_results:
        logger.error("No successful evaluations!")
        return None

    # Create report
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, output_file, logger)
    else:
        create_csv_report(all_results, output_file, logger)

    # Calculate summary statistics
    all_field_results = []
    for results in all_results.values():
        all_field_results.extend(results)

    summary = {}
    if all_field_results:
        total = len(all_field_results)
        correct = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
        incorrect = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
        not_classified = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
        true_data_not_found = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")

        comparable = total - not_classified - true_data_not_found
        accuracy = (correct / comparable * 100) if comparable > 0 else 0

        summary = {
            'total_classifications': total,
            'correct': correct,
            'incorrect': incorrect,
            'not_classified': not_classified,
            'true_data_not_found': true_data_not_found,
            'comparable': comparable,
            'accuracy': accuracy
        }

        logger.info("\n=== CLASSIFICATION EVALUATION SUMMARY ===")
        logger.info(f"Total Classifications: {total}")
        logger.info(f"Correct: {correct}")
        logger.info(f"Incorrect: {incorrect}")
        logger.info(f"Not Classified: {not_classified}")
        logger.info(f"True Data Not Found: {true_data_not_found}")
        logger.info(f"Accuracy: {accuracy:.1f}%")

    logger.info(f"\n📊 Report saved to: {output_file}")
    return {
        'output_file': output_file,
        'summary': summary,
        'total_files': len(all_results)
    }

def test_pdf_finding():
    """Test function to verify PDF finding functionality."""
    logger = setup_logging()

    # Test with some sample filenames
    test_files = ["93557", "150", "1-03132025TT"]

    print("Testing PDF finding functionality:")
    for filename in test_files:
        pdf_path = find_pdf_file(filename, logger)
        if pdf_path:
            print(f"✅ {filename}: Found at {pdf_path}")
        else:
            print(f"❌ {filename}: Not found")

    return True

def run_evaluation_for_subdirectory(subdir_name, true_dir="test_json", output_file=None):
    """
    Run classification evaluation for a specific subdirectory.

    Args:
        subdir_name (str): Name of subdirectory in data/output_data/classification/
        true_dir (str): Directory containing true data files
        output_file (str): Output file path (optional, auto-generated if None)

    Returns:
        dict: Evaluation results summary
    """
    output_dir = f"data/output_data/classification/{subdir_name}"

    if not os.path.exists(output_dir):
        print(f"Error: Directory {output_dir} does not exist")
        return None

    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"data/evaluation/classification_evaluation_{subdir_name}_{timestamp}.xlsx"

    return run_classification_evaluation(output_dir, true_dir, output_file)

def evaluate_all_subdirectories(base_output_dir="data/output_data/classification", true_dir="test_json"):
    """
    Evaluate all subdirectories in the classification output directory.

    Args:
        base_output_dir (str): Base directory containing classification subdirectories
        true_dir (str): Directory containing true data files

    Returns:
        dict: Summary of all evaluations
    """
    if not os.path.exists(base_output_dir):
        print(f"Error: Base directory {base_output_dir} does not exist")
        return None

    # Find all subdirectories and also check main directory
    subdirs = [d for d in os.listdir(base_output_dir)
               if os.path.isdir(os.path.join(base_output_dir, d))]

    # Also check if there are files directly in the main directory
    main_files = [f for f in os.listdir(base_output_dir)
                  if f.endswith('_bedrock_response.json')]

    all_results = {}
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Evaluate main directory if it has files
    if main_files:
        print(f"\n=== Evaluating main directory (found {len(main_files)} files) ===")
        output_file = f"data/evaluation/classification_evaluation_main_{timestamp}.xlsx"

        result = run_classification_evaluation(base_output_dir, true_dir, output_file)
        if result:
            all_results['main'] = result
            print(f"✅ Evaluation completed for main directory")
            print(f"   Accuracy: {result['summary'].get('accuracy', 0):.1f}%")
            print(f"   Total files: {result['total_files']}")
            print(f"   Report: {result['output_file']}")
        else:
            print(f"❌ Evaluation failed for main directory")

    # Evaluate subdirectories
    if subdirs:
        print(f"\nFound subdirectories: {subdirs}")

        for subdir in subdirs:
            print(f"\n=== Evaluating subdirectory: {subdir} ===")
            output_file = f"data/evaluation/classification_evaluation_{subdir}_{timestamp}.xlsx"

            result = run_evaluation_for_subdirectory(subdir, true_dir, output_file)
            if result:
                all_results[subdir] = result
                print(f"✅ Evaluation completed for {subdir}")
                print(f"   Accuracy: {result['summary'].get('accuracy', 0):.1f}%")
                print(f"   Total files: {result['total_files']}")
                print(f"   Report: {result['output_file']}")
            else:
                print(f"❌ Evaluation failed for {subdir}")

    return all_results

def create_summary_report(all_results, output_file=None):
    """
    Create a summary report comparing all evaluations.

    Args:
        all_results (dict): Results from evaluate_all_subdirectories
        output_file (str): Output file path (optional)

    Returns:
        str: Path to summary report
    """
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"data/evaluation/classification_summary_{timestamp}.xlsx"

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    if OPENPYXL_AVAILABLE:
        wb = Workbook()
        ws = wb.active
        ws.title = "Classification Summary"

        # Headers
        headers = ['Directory', 'Total Files', 'Total Classifications', 'Correct', 'Incorrect',
                  'Not Classified', 'True Data Not Found', 'Accuracy (%)', 'Report File']
        ws.append(headers)

        # Style headers
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_font = Font(color="FFFFFF", bold=True)

        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # Add data rows
        row_idx = 2
        for directory, result in all_results.items():
            summary = result.get('summary', {})

            ws.cell(row=row_idx, column=1, value=directory)
            ws.cell(row=row_idx, column=2, value=result.get('total_files', 0))
            ws.cell(row=row_idx, column=3, value=summary.get('total_classifications', 0))
            ws.cell(row=row_idx, column=4, value=summary.get('correct', 0))
            ws.cell(row=row_idx, column=5, value=summary.get('incorrect', 0))
            ws.cell(row=row_idx, column=6, value=summary.get('not_classified', 0))
            ws.cell(row=row_idx, column=7, value=summary.get('true_data_not_found', 0))
            ws.cell(row=row_idx, column=8, value=round(summary.get('accuracy', 0), 2))
            ws.cell(row=row_idx, column=9, value=result.get('output_file', ''))

            row_idx += 1

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if cell.value and len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        wb.save(output_file)
        print(f"Summary report saved to: {output_file}")
    else:
        # Create CSV fallback
        csv_file = output_file.replace('.xlsx', '.csv')
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Directory', 'Total Files', 'Total Classifications', 'Correct', 'Incorrect',
                           'Not Classified', 'True Data Not Found', 'Accuracy (%)', 'Report File'])

            for directory, result in all_results.items():
                summary = result.get('summary', {})
                writer.writerow([
                    directory,
                    result.get('total_files', 0),
                    summary.get('total_classifications', 0),
                    summary.get('correct', 0),
                    summary.get('incorrect', 0),
                    summary.get('not_classified', 0),
                    summary.get('true_data_not_found', 0),
                    round(summary.get('accuracy', 0), 2),
                    result.get('output_file', '')
                ])

        print(f"Summary report saved to: {csv_file}")
        output_file = csv_file

    return output_file

def dry_run_evaluation():
    """
    Perform a dry run to verify the evaluation is working correctly.
    This function tests the evaluation on a small sample and shows the results.
    """
    print("=== DRY RUN: Classification Evaluation ===")

    # Test with main directory (which has matching true data)
    output_dir = "data/output_data/classification"
    true_dir = "test_json"

    print(f"Testing evaluation with:")
    print(f"  Output directory: {output_dir}")
    print(f"  True data directory: {true_dir}")

    # Check if directories exist
    if not os.path.exists(output_dir):
        print(f"❌ Output directory does not exist: {output_dir}")
        return False

    if not os.path.exists(true_dir):
        print(f"❌ True data directory does not exist: {true_dir}")
        return False

    # Find file pairs
    logger = setup_logging()
    file_pairs = find_file_pairs(output_dir, true_dir, logger)

    print(f"\nFound {len(file_pairs)} matching file pairs:")
    for output_file, true_file, base_name in file_pairs[:3]:  # Show first 3
        print(f"  - {base_name}")

    if len(file_pairs) > 3:
        print(f"  ... and {len(file_pairs) - 3} more")

    if not file_pairs:
        print("❌ No matching file pairs found!")
        return False

    # Test evaluation on first file
    print(f"\n=== Testing evaluation on first file: {file_pairs[0][2]} ===")
    output_file, true_file, base_name = file_pairs[0]

    results = evaluate_single_file(output_file, true_file, base_name, logger)

    if results:
        print(f"✅ Evaluation successful!")
        print(f"   Number of page classifications: {len(results)}")

        # Show sample results
        print(f"\n   Sample results:")
        for i, result in enumerate(results[:3]):  # Show first 3 results
            print(f"     Page {result['Field Name']}: {result['Extracted Value']} vs {result['True Data Value']} -> {result['Correct']}")

        if len(results) > 3:
            print(f"     ... and {len(results) - 3} more page classifications")

        # Calculate accuracy for this file
        correct_count = sum(1 for r in results if r['Correct'] == "TRUE")
        total_count = len(results)
        accuracy = (correct_count / total_count * 100) if total_count > 0 else 0
        print(f"   File accuracy: {correct_count}/{total_count} = {accuracy:.1f}%")

        return True
    else:
        print("❌ Evaluation failed!")
        return False

def verify_evaluation_structure():
    """
    Verify that the evaluation structure matches the extraction evaluation format.
    """
    print("=== VERIFICATION: Evaluation Structure ===")

    # Run a quick evaluation
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"data/evaluation/classification_verification_{timestamp}.xlsx"

    result = run_classification_evaluation(
        output_dir="data/output_data/classification",
        true_dir="test_json",
        output_file=output_file
    )

    if result:
        print("✅ Evaluation completed successfully!")
        print(f"   Report saved to: {result['output_file']}")
        print(f"   Total files evaluated: {result['total_files']}")

        summary = result['summary']
        print(f"\n   Summary statistics:")
        print(f"     Total classifications: {summary.get('total_classifications', 0)}")
        print(f"     Correct: {summary.get('correct', 0)}")
        print(f"     Incorrect: {summary.get('incorrect', 0)}")
        print(f"     Accuracy: {summary.get('accuracy', 0):.1f}%")

        print(f"\n✅ Verification complete! The evaluation structure matches the extraction evaluation format.")
        print(f"   - Similar column structure (File Name, Field Name, True Data Value, Extracted Value, Confidence, Correct)")
        print(f"   - Excel report with formatting and statistics")
        print(f"   - Comprehensive logging and error handling")

        return True
    else:
        print("❌ Verification failed!")
        return False

if __name__ == "__main__":
    main()
