#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create test data from CSV file:
1. Downloads PDFs from S3 and merges them by order_id (filtered by attachment types)
2. Creates true data JSON files with document types and page numbers
3. Creates file tracking CSV for metadata
4. Organizes files in order_id folders with individual PDFs
"""

import asyncio
import argparse
import csv
import json
import os
import shutil
from typing import Dict, List, Tuple, Optional
import boto3
from PyPDF2 import PdfMerger, PdfReader
import tempfile
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestDataCreator:
    def __init__(self, bucket_name: str = "tms-upload-prod",
                 allowed_attachment_types: Optional[List[str]] = None,
                 tracking_file_path: Optional[str] = None):
        self.bucket_name = bucket_name
        self.s3_client = boto3.client('s3')
        self.allowed_attachment_types = set(allowed_attachment_types or ["invoice", "bol", "pod"])
        self.file_tracking_data = []
        self.tracking_file_path = tracking_file_path or "./file_tracking_classification.csv"
        self.processed_files = set()  # Track already processed files
        self.load_existing_tracking_data()

    def load_existing_tracking_data(self) -> None:
        """Load existing tracking data to resume processing"""
        if os.path.exists(self.tracking_file_path):
            try:
                with open(self.tracking_file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        if row.get('download_success') == 'True':
                            # Create a unique key for each processed file
                            file_key = f"{row['order_id']}_{row['path_to_file']}"
                            self.processed_files.add(file_key)
                            self.file_tracking_data.append(row)
                logger.info(f"Loaded {len(self.processed_files)} previously processed files from {self.tracking_file_path}")
            except Exception as e:
                logger.warning(f"Could not load existing tracking data: {e}")
        else:
            logger.info(f"No existing tracking file found at {self.tracking_file_path}, starting fresh")

    def is_file_already_processed(self, order_id: str, s3_key: str) -> bool:
        """Check if a file has already been processed"""
        file_key = f"{order_id}_{s3_key}"
        return file_key in self.processed_files

    async def process_single_document(self, doc: Dict, order_id: str,
                                    temp_pdf_path: str, order_folder: str, skipped_folder: str,
                                    merge_page_counter: int, skipped_page_counter: int) -> Dict:
        """Process a single document asynchronously"""
        attachment_type = doc['attachment_type']
        s3_key = doc['path_to_file']
        s3_filename = os.path.basename(s3_key)

        # Download the file
        success, page_count = await self.download_pdf_from_s3(s3_key, temp_pdf_path)

        result = {
            'success': success,
            'page_count': page_count,
            'included_in_merge': False,
            'temp_pdf_path': None,
            'documents': [],
            'merge_page_counter': merge_page_counter,
            'skipped_page_counter': skipped_page_counter
        }

        if success:
            # Check if this should be included in merge and true data
            if attachment_type in self.allowed_attachment_types:
                # Calculate page range for merged documents
                start_page = merge_page_counter
                end_page = merge_page_counter + page_count - 1

                # Create individual file name with page range for allowed types
                if page_count == 1:
                    page_range = str(start_page)
                else:
                    page_range = f"{start_page}-{end_page}"

                individual_filename = f"{page_range}_{attachment_type}_{s3_filename}"
                individual_pdf_path = os.path.join(order_folder, individual_filename)

                # Copy to individual file location
                shutil.copy2(temp_pdf_path, individual_pdf_path)

                # Add to documents list for true data (considering multiple pages)
                doc_entries = []
                for page_num in range(page_count):
                    doc_entries.append({
                        "page_no": merge_page_counter + page_num,
                        "doc_type": attachment_type
                    })

                result.update({
                    'included_in_merge': True,
                    'temp_pdf_path': temp_pdf_path,
                    'documents': doc_entries,
                    'merge_page_counter': merge_page_counter + page_count
                })

                filter_reason = 'included'

                # Record file tracking data
                tracking_record = {
                    **doc,  # Include all original CSV data
                    'processed_timestamp': datetime.now().isoformat(),
                    'download_success': True,
                    'individual_file_path': individual_pdf_path,
                    'page_count': page_count,
                    'page_range': page_range,
                    'merge_start_page': start_page,
                    'merge_end_page': end_page,
                    'included_in_merge': True,
                    'filter_reason': filter_reason
                }

            else:
                # Handle skipped documents
                start_page = skipped_page_counter
                end_page = skipped_page_counter + page_count - 1

                # Create individual file name with page range for skipped types
                if page_count == 1:
                    page_range = str(start_page)
                else:
                    page_range = f"{start_page}-{end_page}"

                individual_filename = f"{page_range}_{attachment_type}_{s3_filename}"
                individual_pdf_path = os.path.join(skipped_folder, individual_filename)

                # Copy to skipped folder
                shutil.copy2(temp_pdf_path, individual_pdf_path)

                result.update({
                    'skipped_page_counter': skipped_page_counter + page_count
                })

                filter_reason = 'attachment_type_not_allowed'

                # Record file tracking data
                tracking_record = {
                    **doc,  # Include all original CSV data
                    'processed_timestamp': datetime.now().isoformat(),
                    'download_success': True,
                    'individual_file_path': individual_pdf_path,
                    'page_count': page_count,
                    'page_range': page_range,
                    'skipped_start_page': start_page,
                    'skipped_end_page': end_page,
                    'included_in_merge': False,
                    'filter_reason': filter_reason
                }

        else:
            # Record failed download
            tracking_record = {
                **doc,
                'processed_timestamp': datetime.now().isoformat(),
                'download_success': False,
                'individual_file_path': '',
                'page_count': 0,
                'page_range': '',
                'included_in_merge': False,
                'filter_reason': 'download_failed'
            }

        self.file_tracking_data.append(tracking_record)

        # Mark file as processed
        file_key = f"{order_id}_{s3_key}"
        self.processed_files.add(file_key)

        return result

    async def download_pdf_from_s3(self, key: str, local_path: str) -> Tuple[bool, int]:
        """Download PDF from S3 to local path and return success status and page count"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                self.s3_client.download_file,
                self.bucket_name,
                key,
                local_path
            )

            # Count pages in the downloaded PDF
            try:
                reader = PdfReader(local_path)
                page_count = len(reader.pages)
            except Exception as e:
                logger.warning(f"Could not count pages in {local_path}: {e}")
                page_count = 1  # Default to 1 page if we can't read it

            logger.info(f"Downloaded {key} to {local_path} ({page_count} pages)")
            return True, page_count
        except Exception as e:
            logger.error(f"Failed to download {key}: {str(e)}")
            return False, 0
    
    def merge_pdfs(self, pdf_paths: List[str], output_path: str) -> bool:
        """Merge multiple PDFs into one"""
        try:
            merger = PdfMerger()
            for pdf_path in pdf_paths:
                if os.path.exists(pdf_path):
                    merger.append(pdf_path)
                else:
                    logger.warning(f"PDF file not found: {pdf_path}")
            
            with open(output_path, 'wb') as output_file:
                merger.write(output_file)
            merger.close()
            logger.info(f"Merged {len(pdf_paths)} PDFs into {output_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to merge PDFs: {str(e)}")
            return False
    
    def create_true_data_json(self, documents: List[Dict], output_path: str) -> bool:
        """Create true data JSON file with proper page numbering"""
        try:
            true_data = {"documents": documents}
            with open(output_path, 'w') as f:
                json.dump(true_data, f, indent=4)
            logger.info(f"Created true data JSON: {output_path} with {len(documents)} document entries")
            return True
        except Exception as e:
            logger.error(f"Failed to create true data JSON: {str(e)}")
            return False

    def create_file_tracking_csv(self, output_path: Optional[str] = None) -> bool:
        """Create file tracking CSV with all metadata"""
        try:
            # Use the configured tracking file path if no output path provided
            file_path = output_path or self.tracking_file_path

            if not self.file_tracking_data:
                logger.warning("No file tracking data to write")
                return True

            # Get all possible fieldnames from all records
            all_fieldnames = set()
            for record in self.file_tracking_data:
                all_fieldnames.update(record.keys())

            # Sort fieldnames for consistent output
            fieldnames = sorted(all_fieldnames)

            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.file_tracking_data)

            logger.info(f"Created file tracking CSV: {file_path} with {len(self.file_tracking_data)} records")
            return True
        except Exception as e:
            logger.error(f"Failed to create file tracking CSV: {str(e)}")
            return False
    
    def get_processed_file_info(self, order_id: str, s3_key: str) -> Dict:
        """Get information about an already processed file from tracking data"""
        file_key = f"{order_id}_{s3_key}"
        for record in self.file_tracking_data:
            if f"{record.get('order_id')}_{record.get('path_to_file')}" == file_key:
                return record
        return {}

    async def process_order(self, order_id: str, order_data: List[Dict],
                          pdf_output_dir: str, json_output_dir: str) -> bool:
        """Process a single order: download ALL PDFs, merge only allowed types, and create true data JSON"""
        logger.info(f"Processing order: {order_id}")

        # Count documents that will be processed for merging
        allowed_docs = [doc for doc in order_data if doc['attachment_type'] in self.allowed_attachment_types]
        logger.info(f"Order {order_id}: {len(allowed_docs)} documents for merging, {len(order_data)} total documents to download")

        # Create order-specific folder for individual PDFs
        order_folder = os.path.join(pdf_output_dir, order_id)
        os.makedirs(order_folder, exist_ok=True)

        # Create skipped folder for non-allowed attachment types
        skipped_folder = os.path.join(order_folder, "skipped")
        os.makedirs(skipped_folder, exist_ok=True)

        # Create temporary directory for downloaded PDFs
        with tempfile.TemporaryDirectory() as temp_dir:
            downloaded_pdfs_for_merge = []
            documents = []
            merge_page_counter = 1   # Page counter for merged PDF only
            skipped_page_counter = 1 # Page counter for skipped files

            # Process ALL documents in original CSV order (download everything)
            # Process ALL documents in original CSV order (sequentially to maintain page order)
            for doc_index, doc in enumerate(order_data):
                s3_key = doc['path_to_file']
                attachment_type = doc['attachment_type']

                # Check if file is already processed
                if self.is_file_already_processed(order_id, s3_key):
                    logger.info(f"Using already processed file: {order_id}/{os.path.basename(s3_key)}")

                    # Get processed file info from tracking data
                    processed_info = self.get_processed_file_info(order_id, s3_key)

                    if processed_info and processed_info.get('download_success') == 'True':
                        page_count = int(processed_info.get('page_count', 1))

                        # Check if this was included in merge
                        if processed_info.get('included_in_merge') == 'True' and attachment_type in self.allowed_attachment_types:
                            # Add to documents list for true data
                            for page_num in range(page_count):
                                documents.append({
                                    "page_no": merge_page_counter + page_num,
                                    "doc_type": attachment_type
                                })

                            # Find the existing individual PDF file
                            individual_pdf_path = processed_info.get('individual_file_path')
                            if individual_pdf_path and os.path.exists(individual_pdf_path):
                                # Create a temporary copy for merging
                                temp_pdf_path = os.path.join(temp_dir, f"{order_id}_{doc_index}.pdf")
                                shutil.copy2(individual_pdf_path, temp_pdf_path)
                                downloaded_pdfs_for_merge.append(temp_pdf_path)

                            merge_page_counter += page_count
                        else:
                            # This was skipped, update skipped counter
                            skipped_page_counter += page_count

                    continue

                # Temporary path for download
                temp_pdf_path = os.path.join(temp_dir, f"{order_id}_{doc_index}.pdf")

                # Process document sequentially to maintain page order
                result = await self.process_single_document(
                    doc, order_id, temp_pdf_path, order_folder, skipped_folder,
                    merge_page_counter, skipped_page_counter
                )

                # Update counters and collect results
                if isinstance(result, dict):
                    if result.get('included_in_merge'):
                        merge_page_counter = result['merge_page_counter']
                        if result.get('temp_pdf_path'):
                            downloaded_pdfs_for_merge.append(result['temp_pdf_path'])
                        if result.get('documents'):
                            documents.extend(result['documents'])
                    else:
                        skipped_page_counter = result['skipped_page_counter']


            if not downloaded_pdfs_for_merge:
                logger.warning(f"No PDFs to merge for order {order_id} (no allowed attachment types)")
                # Still create JSON with documents info even if no PDFs to merge
                json_path = os.path.join(json_output_dir, f"{order_id}.json")
                return self.create_true_data_json(documents, json_path)

            # Merge PDFs (outside the order folder) - only allowed types
            merged_pdf_path = os.path.join(pdf_output_dir, f"{order_id}.pdf")
            merge_success = self.merge_pdfs(downloaded_pdfs_for_merge, merged_pdf_path)

            # Create true data JSON
            json_path = os.path.join(json_output_dir, f"{order_id}.json")
            json_success = self.create_true_data_json(documents, json_path)

            return merge_success and json_success
    
    def read_csv_data(self, csv_path: str) -> Dict[str, List[Dict]]:
        """Read CSV and group data by order_id"""
        orders_data = {}
        
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                order_id = row['order_id']
                if order_id not in orders_data:
                    orders_data[order_id] = []
                orders_data[order_id].append(row)
        
        logger.info(f"Found {len(orders_data)} unique orders in CSV")
        return orders_data
    
    async def process_orders(self, csv_path: str, pdf_output_dir: str,
                           json_output_dir: str, max_orders: Optional[int] = None) -> None:
        """Process multiple orders concurrently"""
        # Create output directories
        os.makedirs(pdf_output_dir, exist_ok=True)
        os.makedirs(json_output_dir, exist_ok=True)

        # Read CSV data
        orders_data = self.read_csv_data(csv_path)

        # Limit number of orders if specified
        if max_orders:
            order_ids = list(orders_data.keys())[:max_orders]
            orders_data = {oid: orders_data[oid] for oid in order_ids}
            logger.info(f"Processing first {max_orders} orders")

        logger.info(f"Allowed attachment types: {sorted(self.allowed_attachment_types)}")

        # Process orders concurrently
        semaphore = asyncio.Semaphore(5)  # Limit concurrent downloads

        async def process_with_semaphore(order_id: str, order_data: List[Dict]):
            async with semaphore:
                return await self.process_order(order_id, order_data, pdf_output_dir, json_output_dir)

        tasks = [
            process_with_semaphore(order_id, order_data)
            for order_id, order_data in orders_data.items()
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Create file tracking CSV
        self.create_file_tracking_csv()

        # Report results
        successful = sum(1 for r in results if r is True)
        failed = len(results) - successful
        logger.info(f"Processing complete: {successful} successful, {failed} failed")
        logger.info(f"File tracking CSV created: {self.tracking_file_path}")

async def create_test_data(
    csv_path: str,
    pdf_output_dir: str,
    json_output_dir: str,
    max_orders: Optional[int] = None,
    allowed_types: Optional[List[str]] = None,
    tracking_file_path: Optional[str] = None
) -> None:
    """
    Simple function to create test data without command line arguments

    Args:
        csv_path: Path to input CSV file
        pdf_output_dir: Directory to store merged PDF files and individual PDFs
        json_output_dir: Directory to store true data JSON files
        max_orders: Maximum number of orders to process (None = all)
        allowed_types: List of allowed attachment types (default: ['invoice', 'bol', 'pod'])
        tracking_file_path: Path to file_tracking_classification.csv (default: './file_tracking_classification.csv')
    """
    if allowed_types is None:
        allowed_types = ['invoice', 'bol', 'pod']

    creator = TestDataCreator(
        allowed_attachment_types=allowed_types,
        tracking_file_path=tracking_file_path
    )
    await creator.process_orders(
        csv_path,
        pdf_output_dir,
        json_output_dir,
        max_orders
    )

async def main():
    parser = argparse.ArgumentParser(description='Create test data from CSV file')
    parser.add_argument('csv_path', help='Path to input CSV file')
    parser.add_argument('pdf_output_dir', help='Directory to store merged PDF files')
    parser.add_argument('json_output_dir', help='Directory to store true data JSON files')
    parser.add_argument('--max-orders', type=int, default=None,
                       help='Maximum number of orders to process (default: all)')
    parser.add_argument('--allowed-types', nargs='+',
                       default=['invoice', 'bol', 'pod'],
                       help='Allowed attachment types to process (default: invoice bol pod)')
    parser.add_argument('--tracking-file', type=str, default=None,
                       help='Path to file_tracking_classification.csv (default: ./file_tracking_classification.csv)')

    args = parser.parse_args()

    await create_test_data(
        args.csv_path,
        args.pdf_output_dir,
        args.json_output_dir,
        args.max_orders,
        args.allowed_types,
        args.tracking_file
    )

if __name__ == "__main__":
    # Option 1: Use the simple function directly (recommended for custom usage)
    asyncio.run(create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./test_output",
        json_output_dir="./test_json",
        max_orders=200,
        allowed_types=["invoice", "bol", "pod", "clear_to_pay", "rate_confirmation", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "POD", "delivery receipt", "fuel_receipt", "customs_doc", "lumper_receipt", "coa", "so_confirmatin", "log", "nmfc_cert", "outgate", "ingate", "pre_pull_receipt"],
        tracking_file_path="./file_tracking_classification.csv"
    ))
'''
BOL = "bol", "Bill of Lading"
POD = "pod", "Proof of Delivery"
RATE_CON = "rate_confirmation", "Carrier Rate Confirmation"
CUST_RATE_CON = "cust_rate_confirmation", "Customer Rate Confirmation"
CLEAR_TO_PAY = "clear_to_pay", "Clear to Pay"
SCALE_TICKET = "scale_ticket", "Scale Ticket"
FREIGHT_BILL = "freight_bill", "Freight Bill"
LOG = "log", "Log"  > Not in input data
FUEL_RECIEPT = "fuel_receipt", "Fuel Receipt" > Not in input data
INVOICE = "invoice", "Carrier Invoice"
INVOICE_CUST = "cust_invoice", "Customer Invoice"   > dont consider
COMBINED_CARRIER_DOCUMENTS = (
 "combined_carrier_documents",
 "Combined Carrier Documents",
)
PACKING_LIST = "pack_list", "Packing List"
PO = "po", "Purchase Order"     > not considered
COMM_INV = "comm_invoice", "Commercial Invoice"     > not considered
CUSTOMS_DOC = "customs_doc", "Customs Document"     > Not in input data
NMFC_CERT = "nmfc_cert", "NMFC Certificate"
OTHER = "other", "Other"
COA = "coa", "Certificate of Analysis"
LOAD_TENDER_FROM_CUST = "tender_from_cust", "Load Tender from Customer"
LUMPER_RECEIPT = "lumper_receipt", "Lumper Receipt"
SO_CONFIRMATION = "so_confirmatin", "Sales Order Confirmation"      > Not in input data
PO_CONFIRMATION = "po_confirmatin", "Purchase Order Confirmation"
PRE_PULL_RECEIPT = "pre_pull_receipt", "Pre-Pull Receipt"       > Not in input data
INGATE = "ingate", "Ingate Document"
OUTGATE = "outgate", "Outgate Document"
'''
'''
attachment_type
other                         17086
combined_carrier_documents     9995
pod                            3218
invoice                        2054
bol                             970
clear to pay                    779
clear_to_pay                    526
rate_confirmation               419
scale_ticket                     27
freight_bill                     27
J1 out                           25
J1 in                            24
cust_invoice                     22
revised invoice                  20
cust_rate_confirmation           13
pack_list                        12
comm_invoice                     12
j1 in                            12
j1 out                           11
po                                9
tender_from_cust                  9
order                             8
CLEAR TO PAY                      8
DO                                8
REVISED INVOICE                   7
POD                               7
BOL signed                        6
Revised invoice                   6
NOA                               6
delivery receipt                  6
Name: count, dtype: int64

should we take? 
    cust_rate_confirmation
    comm_invoice
'''