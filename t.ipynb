{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fe2b30dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking for package updates...\n", "Error updating package list: sudo: a terminal is required to read the password; either use the -S option to read from standard input or configure an askpass helper\n", "sudo: a password is required\n", "\n"]}, {"ename": "SystemExit", "evalue": "1", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[0;31mSystemExit\u001b[0m\u001b[0;31m:\u001b[0m 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Documents/repositories/logistically/.venv/lib/python3.10/site-packages/IPython/core/interactiveshell.py:3587: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": ["import subprocess\n", "import sys\n", "\n", "def check_package_updates():\n", "    # Update the package list\n", "    update_result = subprocess.run(['sudo', 'apt', 'update'], capture_output=True, text=True)\n", "    if update_result.returncode != 0:\n", "        print(\"Error updating package list:\", update_result.stderr)\n", "        sys.exit(1)\n", "    \n", "    # Check for upgradable packages\n", "    list_result = subprocess.run(['apt', 'list', '--upgradable'], capture_output=True, text=True)\n", "    upgradable = list_result.stdout.strip().split('\\n')[1:]  # Skip the header line if present\n", "    \n", "    if upgradable and any(line.strip() for line in upgradable):\n", "        print(\"Package updates are available:\")\n", "        for pkg in upgradable:\n", "            if pkg.strip():\n", "                print(pkg)\n", "    else:\n", "        print(\"No package updates available.\")\n", "\n", "def check_distribution_upgrade():\n", "    # Check for new distribution release\n", "    check_result = subprocess.run(['do-release-upgrade', '-c'], capture_output=True, text=True)\n", "    if \"New release\" in check_result.stdout:\n", "        print(\"A new Ubuntu version is available for upgrade.\")\n", "        print(check_result.stdout)\n", "    elif check_result.returncode != 0:\n", "        print(\"Error checking for distribution upgrade:\", check_result.stderr)\n", "    else:\n", "        print(\"No new Ubuntu version available.\")\n", "\n", "if __name__ == \"__main__\":\n", "    print(\"Checking for package updates...\")\n", "    check_package_updates()\n", "    print(\"\\nChecking for distribution (version) upgrade...\")\n", "    check_distribution_upgrade()\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "fe69039d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}