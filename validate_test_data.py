#!/usr/bin/env python3
"""
Script to validate the created test data
"""

import json
import os
import sys
from pathlib import Path
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>

def validate_test_data(pdf_dir: str, json_dir: str):
    """Validate that PDFs and JSON files are correctly created"""

    pdf_path = Path(pdf_dir)
    json_path = Path(json_dir)

    if not pdf_path.exists():
        print(f"❌ PDF directory does not exist: {pdf_dir}")
        return False

    if not json_path.exists():
        print(f"❌ JSON directory does not exist: {json_dir}")
        return False

    # Get all PDF and JSON files
    pdf_files = list(pdf_path.glob("*.pdf"))
    json_files = list(json_path.glob("*.json"))

    print(f"📁 Found {len(pdf_files)} merged PDF files and {len(json_files)} JSON files")

    # Check for file tracking CSV
    tracking_csv = pdf_path / "file_tracking.csv"
    if tracking_csv.exists():
        print(f"✅ File tracking CSV found: {tracking_csv}")
    else:
        print(f"⚠️  File tracking CSV not found: {tracking_csv}")

    # Check for order folders
    order_folders = [d for d in pdf_path.iterdir() if d.is_dir()]
    print(f"📁 Found {len(order_folders)} order folders")

    # Check that we have matching files
    pdf_order_ids = {f.stem for f in pdf_files}
    json_order_ids = {f.stem for f in json_files}
    folder_order_ids = {f.name for f in order_folders}

    if pdf_order_ids != json_order_ids:
        print(f"❌ Mismatch between PDF and JSON files")
        print(f"   PDF order_ids: {sorted(pdf_order_ids)}")
        print(f"   JSON order_ids: {sorted(json_order_ids)}")
        return False

    if pdf_order_ids != folder_order_ids:
        print(f"⚠️  Mismatch between PDF files and order folders")
        print(f"   PDF order_ids: {sorted(pdf_order_ids)}")
        print(f"   Folder order_ids: {sorted(folder_order_ids)}")

    print(f"✅ All order_ids have both PDF and JSON files")
    
    # Validate each pair
    validation_results = []
    
    for order_id in sorted(pdf_order_ids):
        pdf_file = pdf_path / f"{order_id}.pdf"
        json_file = json_path / f"{order_id}.json"
        
        # Validate PDF
        try:
            reader = PdfReader(str(pdf_file))
            pdf_pages = len(reader.pages)
        except Exception as e:
            print(f"❌ Error reading PDF {order_id}: {e}")
            validation_results.append(False)
            continue
        
        # Validate JSON
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            if 'documents' not in data:
                print(f"❌ JSON {order_id}: Missing 'documents' key")
                validation_results.append(False)
                continue
            
            documents = data['documents']
            json_pages = len(documents)
            
            # Check page numbers are sequential
            expected_pages = list(range(1, json_pages + 1))
            actual_pages = [doc['page_no'] for doc in documents]
            
            if actual_pages != expected_pages:
                print(f"❌ JSON {order_id}: Page numbers not sequential")
                print(f"   Expected: {expected_pages}")
                print(f"   Actual: {actual_pages}")
                validation_results.append(False)
                continue
            
            # Check all documents have doc_type
            for i, doc in enumerate(documents):
                if 'doc_type' not in doc:
                    print(f"❌ JSON {order_id}: Document {i+1} missing 'doc_type'")
                    validation_results.append(False)
                    continue
            
        except Exception as e:
            print(f"❌ Error reading JSON {order_id}: {e}")
            validation_results.append(False)
            continue
        
        # Check individual files in order folder
        order_folder = pdf_path / order_id
        if order_folder.exists():
            individual_files = sorted(list(order_folder.glob("*.pdf")))
            print(f"   📄 Order folder {order_id}: {len(individual_files)} individual PDF files")

            # Validate individual file naming (should start with sequence number)
            for i, individual_file in enumerate(individual_files):
                filename = individual_file.name
                expected_prefix = f"{i + 1}_"
                if not filename.startswith(expected_prefix):
                    print(f"   ⚠️  Individual file naming issue: {filename} (expected to start with {expected_prefix})")
                else:
                    print(f"   ✅ {filename}")
        else:
            print(f"   ⚠️  Order folder missing: {order_folder}")

        # Check if PDF pages match JSON document entries
        if pdf_pages != json_pages:
            print(f"   ℹ️  Order {order_id}: PDF has {pdf_pages} pages, JSON has {json_pages} document entries")

        print(f"✅ Order {order_id}: PDF ({pdf_pages} pages), JSON ({json_pages} document entries)")
        validation_results.append(True)
    
    # Summary
    successful = sum(validation_results)
    total = len(validation_results)
    
    print(f"\n📊 Validation Summary:")
    print(f"   Total orders: {total}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {total - successful}")
    
    if successful == total:
        print(f"🎉 All test data validated successfully!")
        return True
    else:
        print(f"❌ Some validation errors found")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python validate_test_data.py <pdf_dir> <json_dir>")
        print("Example: python validate_test_data.py ./test_input_pdfs ./test_true_data")
        sys.exit(1)
    
    pdf_dir = sys.argv[1]
    json_dir = sys.argv[2]
    
    success = validate_test_data(pdf_dir, json_dir)
    sys.exit(0 if success else 1)
