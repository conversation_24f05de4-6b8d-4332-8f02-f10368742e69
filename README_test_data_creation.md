# Test Data Creation Scripts

This repository contains scripts to create test data from a CSV file containing order information and S3 file paths.

## Overview

The scripts will:
1. **Download ALL documents**: Downloads every PDF from S3, regardless of attachment type
2. **Filter for merging**: Only merge documents with specified attachment types (default: invoice, bol, pod)
3. **Create merged PDFs**: Merge only filtered PDFs into a single PDF file per order
4. **Create individual PDF files**: Store each PDF individually with global page numbering
5. **Create true data JSON files**: Generate JSON files with document type and page number information (only for merged documents)
6. **Create file tracking CSV**: Record all metadata and processing details for complete traceability

## Files

- `create_test_data.py` - Main Python script for processing orders
- `test_data_creation.py` - Simple test script that processes 4 orders
- `run_test_data_creation.sh` - Bash script wrapper for easy command-line usage
- `requirements_test_data.txt` - Python dependencies

## Prerequisites

1. **Python Dependencies**: Install required packages
   ```bash
   pip install -r requirements_test_data.txt
   ```

2. **AWS Credentials**: Ensure AWS credentials are configured for S3 access
   ```bash
   aws configure
   # or ensure ~/.aws/credentials is properly set up
   ```

3. **S3 Access**: The script accesses the `tms-upload-prod` S3 bucket

## Usage

### Method 1: Using the bash script (Recommended)
```bash
./run_test_data_creation.sh <pdf_output_dir> <json_output_dir> [max_orders] [allowed_types]
```

**Examples:**
```bash
# Process 4 orders with default types (invoice, bol, pod)
./run_test_data_creation.sh ./input_pdfs ./true_data

# Process 10 orders with default types
./run_test_data_creation.sh ./input_pdfs ./true_data 10

# Process 5 orders with custom attachment types
./run_test_data_creation.sh ./input_pdfs ./true_data 5 "invoice bol pod rate_confirmation"

# Process all orders with all attachment types (warning: this could be thousands!)
./run_test_data_creation.sh ./input_pdfs ./true_data 0 "invoice bol pod combined_carrier_documents other"
```

### Method 2: Using Python directly
```bash
python create_test_data.py <csv_path> <pdf_output_dir> <json_output_dir> --max-orders <number> --allowed-types <types>
```

**Examples:**
```bash
# Default attachment types (invoice, bol, pod)
python create_test_data.py \
  "/path/to/csv/file.csv" \
  "./input_pdfs" \
  "./true_data" \
  --max-orders 5

# Custom attachment types
python create_test_data.py \
  "/path/to/csv/file.csv" \
  "./input_pdfs" \
  "./true_data" \
  --max-orders 10 \
  --allowed-types invoice bol pod rate_confirmation
```

### Method 3: Using the function directly (Recommended)
```python
import asyncio
from create_test_data import create_test_data

# Basic usage with resume functionality
await create_test_data(
    csv_path="/path/to/csv/file.csv",
    pdf_output_dir="./output",
    json_output_dir="./json",
    max_orders=5,
    allowed_types=["invoice", "bol", "pod"],
    tracking_file_path="./my_tracking.csv"
)

# Run again to resume processing (skips already processed files)
await create_test_data(
    csv_path="/path/to/csv/file.csv",
    pdf_output_dir="./output",
    json_output_dir="./json",
    max_orders=10,  # Process more orders
    allowed_types=["invoice", "bol", "pod"],
    tracking_file_path="./my_tracking.csv"  # Same tracking file
)
```

### Method 4: Quick test with predefined settings
```bash
python test_data_creation.py
```

## Output Structure

### Merged PDFs
- **Location**: Specified PDF output directory (root level)
- **Naming**: `{order_id}.pdf` (e.g., `********.pdf`)
- **Content**: All filtered PDFs for an order merged in the sequence they appear in the CSV file

### Individual PDF Files
- **Location**: `{pdf_output_dir}/{order_id}/` folders
- **Naming**: `{page_range}_{attachment_type}_{s3_filename}.pdf`
- **Page Numbering**: Global page numbers across all documents in the order
- **Examples**:
  - `1_bol_QURP3ZX2MQDF6YHG4HGJ.pdf` (single page document)
  - `2-4_combined_carrier_documents_KDW8XV8I2BAVJ12TQI4V.pdf` (3-page document)
  - `5_invoice_GM0UFYL1SFVLJ0WVUDTG.pdf` (single page document)
  - `6_pod_NUI776G8YF6A52K8UGM2.pdf` (single page document)

### True Data JSON Files
- **Location**: Specified JSON output directory
- **Naming**: `{order_id}.json` (e.g., `********.json`)
- **Structure**: Accounts for multi-page documents
  ```json
  {
      "documents": [
          {
              "page_no": 1,
              "doc_type": "bol"
          },
          {
              "page_no": 2,
              "doc_type": "combined_carrier_documents"
          },
          {
              "page_no": 3,
              "doc_type": "combined_carrier_documents"
          },
          {
              "page_no": 4,
              "doc_type": "invoice"
          }
      ]
  }
  ```

### File Tracking CSV
- **Location**: Configurable (default: `./file_tracking_classification.csv`)
- **Resume Functionality**: Automatically loads existing data to skip processed files
- **Content**: Complete metadata for all processed files including:
  - All original CSV columns
  - Processing timestamp
  - Download success status
  - Individual file path
  - Page count and page range
  - Merge/skipped start/end page numbers
  - Whether included in merge
  - Filter reason (included/excluded/failed)

## CSV File Format

The input CSV file should have these columns:
- `order_id` - Unique identifier for each order
- `attachment_type` - Document type (bol, invoice, pod, etc.)
- `path_to_file` - S3 key path to the PDF file

## Features

- **Download All Documents**: Downloads every PDF regardless of attachment type for complete archive
- **Selective Merging**: Only merges documents with specified attachment types
- **Sequential Page Numbering**: Individual files named with sequential page numbers within each order
- **Skipped File Organization**: Non-allowed attachment types stored in separate `skipped` folders
- **Resume Functionality**: Automatically skips already processed files on subsequent runs
- **Async Order Processing**: Processes multiple orders concurrently while maintaining page order within each order
- **Multi-page Document Support**: Correctly handles PDFs with multiple pages in JSON output
- **Individual File Organization**: Creates structured folders with individual PDFs
- **Configurable Tracking File**: Custom location for `file_tracking_classification.csv`
- **Complete Traceability**: File tracking CSV records all processing metadata
- **Error Handling**: Continues processing even if some files fail to download
- **Progress Logging**: Detailed logging of download and merge operations
- **Function-based API**: Can be called directly as a function without command line

## Example Test Run

The test run processed 2 orders successfully with filtering:
- `********.pdf` (5 pages) - 4 documents (1 bol, 2 combined_carrier_documents, 1 invoice, 1 pod)
- `11629776.pdf` (6 pages) - 4 documents (1 bol, 3 combined_carrier_documents, 1 invoice, 1 pod)

**Output Structure:**
```
test_filtered_all_types/
├── ********.pdf                    # Merged PDF
├── 11629776.pdf                    # Merged PDF
├── file_tracking.csv               # Complete metadata
├── ********/                       # Individual files folder
│   ├── 1_bol_QURP3ZX2MQDF6YHG4HGJ.pdf
│   ├── 2_combined_carrier_documents_C3HFBNAA9ZWNU3X2CCLG.pdf
│   ├── 3_invoice_GM0UFYL1SFVLJ0WVUDTG.pdf
│   └── 4_pod_NUI776G8YF6A52K8UGM2.pdf
└── 11629776/                       # Individual files folder
    ├── 1_bol_SHDVW6MQWR0YPGYI1JJ9.pdf
    ├── 2_combined_carrier_documents_KDW8XV8I2BAVJ12TQI4V.pdf
    ├── 3_invoice_Q2CBUISBWTQ91NQZ07P0.pdf
    └── 4_pod_VQNIUYCLPIMOF88L25L7.pdf
```

## Troubleshooting

1. **AWS Credentials Error**: Ensure AWS credentials are properly configured
2. **S3 Access Denied**: Verify you have read access to the `tms-upload-prod` bucket
3. **File Not Found**: Some S3 keys in the CSV might not exist - the script will log errors and continue
4. **Memory Issues**: For large numbers of orders, consider processing in smaller batches
