#!/bin/bash

# <PERSON>ript to run test data creation with command line arguments
# Usage: ./run_test_data_creation.sh <pdf_output_dir> <json_output_dir> [max_orders] [allowed_types...]

set -e

# Default values
CSV_PATH="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv"
MAX_ORDERS=${3:-4}  # Default to 4 orders if not specified
ALLOWED_TYPES=${4:-"invoice bol pod"}  # Default allowed types

# Check arguments
if [ $# -lt 2 ]; then
    echo "Usage: $0 <pdf_output_dir> <json_output_dir> [max_orders] [allowed_types]"
    echo "Example: $0 ./input_pdfs ./true_data 5 \"invoice bol pod\""
    echo "Example: $0 ./input_pdfs ./true_data 10 \"invoice bol pod rate_confirmation\""
    exit 1
fi

PDF_OUTPUT_DIR=$1
JSON_OUTPUT_DIR=$2

echo "Creating test data..."
echo "CSV file: $CSV_PATH"
echo "PDF output directory: $PDF_OUTPUT_DIR"
echo "JSON output directory: $JSON_OUTPUT_DIR"
echo "Max orders to process: $MAX_ORDERS"
echo "Allowed attachment types: $ALLOWED_TYPES"
echo ""

# Run the Python script
python create_test_data.py "$CSV_PATH" "$PDF_OUTPUT_DIR" "$JSON_OUTPUT_DIR" --max-orders "$MAX_ORDERS" --allowed-types $ALLOWED_TYPES

echo ""
echo "Test data creation completed!"
echo "Merged PDFs are in: $PDF_OUTPUT_DIR"
echo "Individual PDFs are in order folders within: $PDF_OUTPUT_DIR"
echo "True data JSON files are in: $JSON_OUTPUT_DIR"
echo "File tracking CSV is at: $PDF_OUTPUT_DIR/file_tracking.csv"
