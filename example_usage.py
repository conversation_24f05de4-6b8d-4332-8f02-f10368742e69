#!/usr/bin/env python3
"""
Example usage of the create_test_data function with new features
"""

import asyncio
from app.create_test_data import create_test_data

async def main():
    """Example usage of the create_test_data function with resume functionality"""

    # Example 1: Basic usage with custom tracking file
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./example_output",
        json_output_dir="./example_json",
        max_orders=5,
        tracking_file_path="./example_tracking.csv"
    )

    # Example 2: Resume processing (run this again and it will skip already processed files)
    print("Running again to demonstrate resume functionality...")
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./example_output",
        json_output_dir="./example_json",
        max_orders=10,  # Process more orders this time
        tracking_file_path="./example_tracking.csv"  # Same tracking file
    )

    # Example 3: Custom attachment types with different tracking file
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./custom_output",
        json_output_dir="./custom_json",
        max_orders=3,
        allowed_types=["invoice", "bol", "pod", "combined_carrier_documents"],
        tracking_file_path="./custom_tracking.csv"
    )

    # Example 4: Process all orders with specific types
    await create_test_data(
        csv_path="/home/<USER>/Documents/repositories/logistically/docs/other_docs_from_combined_documents_7.csv/other_docs_from_combined_documents_7.csv",
        pdf_output_dir="./all_invoices",
        json_output_dir="./all_invoices_json",
        max_orders=None,  # Process all orders
        allowed_types=["invoice"],  # Only invoices
        tracking_file_path="./invoice_tracking.csv"
    )

if __name__ == "__main__":
    asyncio.run(main())
